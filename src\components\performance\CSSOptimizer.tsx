'use client'

import { useEffect } from 'react'

export default function CSSOptimizer() {
  useEffect(() => {
    // Defer non-critical CSS loading
    const loadNonCriticalCSS = () => {
      // Load non-critical styles after initial render
      const nonCriticalElements = document.querySelectorAll('.defer-load')

      // Use Intersection Observer for progressive loading
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('loaded')
            observer.unobserve(entry.target)
          }
        })
      }, {
        rootMargin: '50px'
      })

      nonCriticalElements.forEach((el) => observer.observe(el))

      // Advanced CSS loading strategy
      const loadStylesheet = (href: string, media = 'all') => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = href
        link.media = media

        // Use onload to switch media from 'print' to 'all' for non-blocking load
        if (media !== 'all') {
          link.onload = function() {
            link.media = 'all'
          }
        }

        document.head.appendChild(link)
        return link
      }

      // Load non-critical CSS with print media trick
      const nonCriticalCSS = [
        '/styles/non-critical.css',
      ]

      nonCriticalCSS.forEach(href => {
        // Check if stylesheet is already loaded
        const existing = document.querySelector(`link[href="${href}"]`)
        if (!existing) {
          loadStylesheet(href, 'print')
        }
      })

      // Preload critical CSS for faster subsequent page loads
      const criticalCSS = [
        '/styles/critical.css',
      ]

      criticalCSS.forEach(href => {
        const preloadLink = document.createElement('link')
        preloadLink.rel = 'preload'
        preloadLink.as = 'style'
        preloadLink.href = href
        document.head.appendChild(preloadLink)
      })
    }

    // Load non-critical CSS after initial paint
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', loadNonCriticalCSS)
    } else {
      // Use requestIdleCallback if available, otherwise setTimeout
      if ('requestIdleCallback' in window) {
        requestIdleCallback(loadNonCriticalCSS)
      } else {
        setTimeout(loadNonCriticalCSS, 100)
      }
    }

    // Optimize font loading
    const optimizeFonts = () => {
      // Add font-display: swap to any @font-face rules
      const styleSheets = Array.from(document.styleSheets)
      
      styleSheets.forEach((sheet) => {
        try {
          const rules = Array.from(sheet.cssRules || sheet.rules || [])
          rules.forEach((rule) => {
            if (rule.type === CSSRule.FONT_FACE_RULE) {
              const fontFaceRule = rule as CSSFontFaceRule
              if (!(fontFaceRule.style as any).fontDisplay) {
                (fontFaceRule.style as any).fontDisplay = 'swap'
              }
            }
          })
        } catch (e) {
          // Cross-origin stylesheets may throw errors
          console.debug('Could not access stylesheet:', e)
        }
      })
    }

    // Run font optimization after a short delay
    setTimeout(optimizeFonts, 100)

    // Cleanup function
    return () => {
      document.removeEventListener('DOMContentLoaded', loadNonCriticalCSS)
    }
  }, [])

  return null
}
