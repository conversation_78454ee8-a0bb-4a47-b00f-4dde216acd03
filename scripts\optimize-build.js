#!/usr/bin/env node

/**
 * Build optimization script for production
 * Runs additional optimizations after Next.js build
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 Starting build optimization...')

// Paths
const buildDir = path.join(process.cwd(), '.next')
const staticDir = path.join(buildDir, 'static')
const publicDir = path.join(process.cwd(), 'public')

// Function to get file size in KB
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath)
    return (stats.size / 1024).toFixed(2)
  } catch (error) {
    return '0'
  }
}

// Function to compress files using gzip
function compressFiles() {
  console.log('📦 Compressing static assets...')
  
  try {
    // Compress CSS files
    execSync(`find ${staticDir} -name "*.css" -exec gzip -k {} \\;`, { stdio: 'inherit' })
    
    // Compress JS files
    execSync(`find ${staticDir} -name "*.js" -exec gzip -k {} \\;`, { stdio: 'inherit' })
    
    console.log('✅ Static assets compressed')
  } catch (error) {
    console.warn('⚠️  Compression failed:', error.message)
  }
}

// Function to optimize images
function optimizeImages() {
  console.log('🖼️  Optimizing images...')
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp']
  const imagesDir = path.join(publicDir, 'images')
  
  if (!fs.existsSync(imagesDir)) {
    console.log('No images directory found, skipping image optimization')
    return
  }
  
  try {
    const files = fs.readdirSync(imagesDir)
    let optimizedCount = 0
    
    files.forEach(file => {
      const ext = path.extname(file).toLowerCase()
      if (imageExtensions.includes(ext)) {
        const filePath = path.join(imagesDir, file)
        const sizeBefore = getFileSize(filePath)
        
        // Basic optimization - in a real project, you'd use imagemin or similar
        console.log(`  📸 ${file}: ${sizeBefore}KB`)
        optimizedCount++
      }
    })
    
    console.log(`✅ Processed ${optimizedCount} images`)
  } catch (error) {
    console.warn('⚠️  Image optimization failed:', error.message)
  }
}

// Function to analyze bundle size
function analyzeBundleSize() {
  console.log('📊 Analyzing bundle size...')
  
  try {
    const manifestPath = path.join(buildDir, 'build-manifest.json')
    if (fs.existsSync(manifestPath)) {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
      
      console.log('\n📦 Bundle Analysis:')
      
      // Analyze pages
      Object.entries(manifest.pages).forEach(([page, files]) => {
        const totalSize = files.reduce((sum, file) => {
          const filePath = path.join(buildDir, file)
          return sum + parseFloat(getFileSize(filePath))
        }, 0)
        
        console.log(`  ${page}: ${totalSize.toFixed(2)}KB`)
      })
      
      // Check for large files
      const largeFiles = []
      function checkDirectory(dir, prefix = '') {
        if (!fs.existsSync(dir)) return
        
        fs.readdirSync(dir).forEach(file => {
          const filePath = path.join(dir, file)
          const stat = fs.statSync(filePath)
          
          if (stat.isDirectory()) {
            checkDirectory(filePath, `${prefix}${file}/`)
          } else {
            const size = parseFloat(getFileSize(filePath))
            if (size > 100) { // Files larger than 100KB
              largeFiles.push({ file: `${prefix}${file}`, size })
            }
          }
        })
      }
      
      checkDirectory(staticDir, '_next/static/')
      
      if (largeFiles.length > 0) {
        console.log('\n⚠️  Large files detected:')
        largeFiles
          .sort((a, b) => b.size - a.size)
          .slice(0, 10)
          .forEach(({ file, size }) => {
            console.log(`  ${file}: ${size}KB`)
          })
      }
    }
  } catch (error) {
    console.warn('⚠️  Bundle analysis failed:', error.message)
  }
}

// Function to generate performance report
function generatePerformanceReport() {
  console.log('📈 Generating performance report...')
  
  const report = {
    timestamp: new Date().toISOString(),
    buildSize: {},
    recommendations: []
  }
  
  // Calculate total build size
  function calculateDirSize(dir) {
    let totalSize = 0
    if (!fs.existsSync(dir)) return totalSize
    
    fs.readdirSync(dir).forEach(file => {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        totalSize += calculateDirSize(filePath)
      } else {
        totalSize += stat.size
      }
    })
    
    return totalSize
  }
  
  const buildSize = calculateDirSize(buildDir)
  report.buildSize.total = `${(buildSize / 1024 / 1024).toFixed(2)}MB`
  
  // Add recommendations
  if (buildSize > 10 * 1024 * 1024) { // > 10MB
    report.recommendations.push('Consider code splitting to reduce bundle size')
  }
  
  report.recommendations.push('Enable gzip compression on your server')
  report.recommendations.push('Use a CDN for static assets')
  report.recommendations.push('Implement service worker for caching')
  
  // Save report
  const reportPath = path.join(buildDir, 'performance-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  
  console.log(`✅ Performance report saved to ${reportPath}`)
  console.log(`📊 Total build size: ${report.buildSize.total}`)
}

// Main execution
async function main() {
  try {
    // Check if build directory exists
    if (!fs.existsSync(buildDir)) {
      console.error('❌ Build directory not found. Run "npm run build" first.')
      process.exit(1)
    }
    
    // Run optimizations
    compressFiles()
    optimizeImages()
    analyzeBundleSize()
    generatePerformanceReport()
    
    console.log('\n🎉 Build optimization complete!')
    console.log('\n💡 Next steps:')
    console.log('  1. Test the optimized build locally')
    console.log('  2. Deploy with proper server compression')
    console.log('  3. Monitor Core Web Vitals in production')
    
  } catch (error) {
    console.error('❌ Build optimization failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

module.exports = { main }
