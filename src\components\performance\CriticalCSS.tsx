// Critical CSS component for above-the-fold content
// This inlines the most critical styles to prevent render blocking

export default function CriticalCSS() {
  return (
    <style dangerouslySetInnerHTML={{
      __html: `
      /* Critical base styles - inline to prevent render blocking */
      html {
        font-family: var(--font-barlow), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-display: swap;
      }
      
      body {
        margin: 0;
        padding: 0;
        color: #000;
        background-color: #f0ebde;
        font-family: var(--font-barlow), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
        line-height: 1.6;
      }

      /* Critical hero section styles */
      .text-hero {
        font-size: 4rem;
        line-height: 1.1;
        letter-spacing: -0.02em;
        font-weight: 700;
        font-family: var(--font-barlow), sans-serif;
        contain: layout style paint;
        margin: 0;
        padding: 0;
      }

      /* Critical navigation styles */
      .nav-critical {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 50;
        background-color: #f0ebde;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 2rem;
      }

      /* Critical responsive styles */
      @media (max-width: 768px) {
        .text-hero {
          font-size: 2.5rem;
        }
        .nav-critical {
          padding: 0 1rem;
        }
      }

      @media (max-width: 640px) {
        .text-hero {
          font-size: 2rem;
        }
      }

      /* Critical layout styles to prevent CLS */
      .hero-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 80px; /* Account for fixed nav */
      }

      /* Critical button styles */
      .btn-primary {
        display: inline-block;
        padding: 0.875rem 1.5rem;
        border: 2px solid #000;
        background-color: transparent;
        color: #000;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        text-decoration: none;
        border-radius: 1rem;
        transition: all 0.2s ease;
        cursor: pointer;
      }

      .btn-primary:hover {
        background-color: #000;
        color: #f0ebde;
      }

      /* Prevent flash of unstyled content */
      .loading-placeholder {
        opacity: 0;
        animation: fadeIn 0.3s ease-in-out forwards;
      }

      @keyframes fadeIn {
        to {
          opacity: 1;
        }
      }

      /* Critical grid and layout utilities */
      .container {
        max-width: 80rem;
        margin: 0 auto;
        padding: 0 1rem;
      }

      .flex {
        display: flex;
      }

      .items-center {
        align-items: center;
      }

      .justify-center {
        justify-content: center;
      }

      .justify-between {
        justify-content: space-between;
      }

      .text-center {
        text-align: center;
      }

      .font-bold {
        font-weight: 700;
      }

      .uppercase {
        text-transform: uppercase;
      }

      .tracking-wide {
        letter-spacing: 0.05em;
      }

      /* Hide non-critical content initially to improve LCP */
      .defer-load {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.3s ease, transform 0.3s ease;
      }

      .defer-load.loaded {
        opacity: 1;
        transform: translateY(0);
      }
      `
    }} />
  )
}
