[{"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx": "5", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\compositions\\OrganicComposition.tsx": "6", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Footer.tsx": "7", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Header.tsx": "8", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\PageWrapper.tsx": "9", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\PerformanceMonitor.tsx": "10", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\AnimatedShapes.tsx": "11", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Circle.tsx": "12", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\HalfCircle.tsx": "13", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Rectangle.tsx": "14", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\RoundedShapes.tsx": "15", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Triangle.tsx": "16", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\hooks\\useScrollAnimation.ts": "17", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\middleware.ts": "18", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\BFCacheOptimizer.tsx": "19", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\CriticalCSS.tsx": "20", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\CSSOptimizer.tsx": "21"}, {"size": 3945, "mtime": 1751368348484, "results": "22", "hashOfConfig": "23"}, {"size": 5800, "mtime": 1751368397355, "results": "24", "hashOfConfig": "23"}, {"size": 1983, "mtime": 1751547504382, "results": "25", "hashOfConfig": "23"}, {"size": 46447, "mtime": 1751545837760, "results": "26", "hashOfConfig": "23"}, {"size": 5956, "mtime": 1751366727918, "results": "27", "hashOfConfig": "23"}, {"size": 7017, "mtime": 1751465060420, "results": "28", "hashOfConfig": "23"}, {"size": 1338, "mtime": 1751365816717, "results": "29", "hashOfConfig": "23"}, {"size": 3248, "mtime": 1751400818384, "results": "30", "hashOfConfig": "23"}, {"size": 419, "mtime": 1751363944301, "results": "31", "hashOfConfig": "23"}, {"size": 5548, "mtime": 1751547106264, "results": "32", "hashOfConfig": "23"}, {"size": 6976, "mtime": 1751403724820, "results": "33", "hashOfConfig": "23"}, {"size": 949, "mtime": 1751366318919, "results": "34", "hashOfConfig": "23"}, {"size": 1302, "mtime": 1751363912836, "results": "35", "hashOfConfig": "23"}, {"size": 1411, "mtime": 1751366358877, "results": "36", "hashOfConfig": "23"}, {"size": 3823, "mtime": 1751402711577, "results": "37", "hashOfConfig": "23"}, {"size": 2642, "mtime": 1751365661262, "results": "38", "hashOfConfig": "23"}, {"size": 4067, "mtime": 1751465134724, "results": "39", "hashOfConfig": "23"}, {"size": 2242, "mtime": 1751546877947, "results": "40", "hashOfConfig": "23"}, {"size": 4965, "mtime": 1751547336540, "results": "41", "hashOfConfig": "23"}, {"size": 3821, "mtime": 1751547304682, "results": "42", "hashOfConfig": "23"}, {"size": 3466, "mtime": 1751547530220, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dzgleq", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx", ["107"], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\page.tsx", ["108", "109", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119"], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\compositions\\OrganicComposition.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\PageWrapper.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\PerformanceMonitor.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\AnimatedShapes.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Circle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\HalfCircle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Rectangle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\RoundedShapes.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Triangle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\hooks\\useScrollAnimation.ts", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\BFCacheOptimizer.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\CriticalCSS.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\CSSOptimizer.tsx", [], [], {"ruleId": "120", "severity": 1, "message": "121", "line": 41, "column": 9, "nodeType": "122", "endLine": 41, "endColumn": 69}, {"ruleId": "123", "severity": 1, "message": "124", "line": 422, "column": 29, "nodeType": "122", "endLine": 422, "endColumn": 248}, {"ruleId": "123", "severity": 1, "message": "124", "line": 436, "column": 29, "nodeType": "122", "endLine": 436, "endColumn": 238}, {"ruleId": "123", "severity": 1, "message": "124", "line": 450, "column": 29, "nodeType": "122", "endLine": 450, "endColumn": 236}, {"ruleId": "123", "severity": 1, "message": "124", "line": 464, "column": 29, "nodeType": "122", "endLine": 464, "endColumn": 248}, {"ruleId": "123", "severity": 1, "message": "124", "line": 488, "column": 29, "nodeType": "122", "endLine": 488, "endColumn": 250}, {"ruleId": "123", "severity": 1, "message": "124", "line": 502, "column": 29, "nodeType": "122", "endLine": 502, "endColumn": 250}, {"ruleId": "123", "severity": 1, "message": "124", "line": 516, "column": 29, "nodeType": "122", "endLine": 516, "endColumn": 244}, {"ruleId": "123", "severity": 1, "message": "124", "line": 530, "column": 29, "nodeType": "122", "endLine": 530, "endColumn": 238}, {"ruleId": "123", "severity": 1, "message": "124", "line": 558, "column": 29, "nodeType": "122", "endLine": 558, "endColumn": 240}, {"ruleId": "123", "severity": 1, "message": "124", "line": 572, "column": 29, "nodeType": "122", "endLine": 572, "endColumn": 241}, {"ruleId": "123", "severity": 1, "message": "124", "line": 596, "column": 29, "nodeType": "122", "endLine": 596, "endColumn": 236}, {"ruleId": "123", "severity": 1, "message": "124", "line": 610, "column": 29, "nodeType": "122", "endLine": 610, "endColumn": 240}, "@next/next/google-font-preconnect", "`rel=\"preconnect\"` is missing from Google Font. See: https://nextjs.org/docs/messages/google-font-preconnect", "JSXOpeningElement", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element"]